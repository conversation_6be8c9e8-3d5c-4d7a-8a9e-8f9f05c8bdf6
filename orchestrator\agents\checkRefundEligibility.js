import { DynamicTool } from "langchain/tools";
import axios from "axios";
import { parseString } from "xml2js"; // We'll need to add this dependency

// Constants
const API_CONFIG = {
  baseURL: "http://localhost:5120",
  endpoints: {
    refundEligibility: "/Refund/boombox-search"
  },
  timeout: 100000
};

const ERROR_MESSAGES = {
  INVALID_INPUT: "Invalid input. Please provide valid booking details.",
  INVALID_PNR: "❌ PNR is not correct. PNR should be 6 characters long and contain only alphanumeric characters.",
  API_ERROR: "❌ Failed to reach the refund eligibility service. Please try again later.",
  NO_RESULTS: "⚠️ No eligibility information found for the given booking.",
  UNEXPECTED: "❌ Something went wrong while checking refund eligibility."
};

/**
 * Validates PNR format
 * @param {string} pnr - PNR to validate
 * @returns {boolean} Whether PNR is valid (6 characters, alphanumeric)
 */
const validatePnr = (pnr) => {
  if (!pnr || typeof pnr !== 'string') {
    return false;
  }

  const trimmedPnr = pnr.trim();

  // Check if PNR is exactly 6 characters long and contains only alphanumeric characters
  return /^[A-Za-z0-9]{6}$/.test(trimmedPnr);
};

/**
 * Extracts PNR from input data
 * @param {Object} data - Input data object
 * @returns {string|null} Extracted PNR or null if not found
 */
const extractPnrFromInput = (data) => {
  // Check various possible locations for PNR
  if (data.pnr) return data.pnr;
  if (data.PNR) return data.PNR;
  if (data.bookingReference) return data.bookingReference;
  if (data.bookingRef) return data.bookingRef;
  if (data.recordLocator) return data.recordLocator;

  // Check nested data object
  if (data.data) {
    if (data.data.pnr) return data.data.pnr;
    if (data.data.PNR) return data.data.PNR;
    if (data.data.bookingReference) return data.data.bookingReference;
    if (data.data.bookingRef) return data.data.bookingRef;
    if (data.data.recordLocator) return data.data.recordLocator;
  }

  // Check flight details
  if (data.flightDetails) {
    if (data.flightDetails.pnr) return data.flightDetails.pnr;
    if (data.flightDetails.bookingReference) return data.flightDetails.bookingReference;
    if (data.flightDetails.recordLocator) return data.flightDetails.recordLocator;
  }

  return null;
};

/**
 * Parses XML string to JavaScript object
 * @param {string} xmlString - XML string to parse
 * @returns {Promise<Object>} Parsed XML as JavaScript object
 */
const parseXML = (xmlString) => {
  return new Promise((resolve, reject) => {
    parseString(xmlString, { explicitArray: false }, (err, result) => {
      if (err) {
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/**
 * Extracts refund information from XML response
 * @param {string} xmlString - XML response from API
 * @returns {Promise<Object>} Extracted refund information
 */
const extractRefundInfoFromXML = async (xmlString) => {
  try {
    console.log("🔄 Parsing XML response");
    
    // Clean up XML string if needed
    const cleanXml = xmlString.replace(/\\n/g, '').replace(/\\\"/g, '"');
    
    // Parse XML to JavaScript object
    const parsedXml = await parseXML(cleanXml);
    console.log("✅ XML parsed successfully");
    
    // Extract refund information
    const result = parsedXml.result;
    const refund = result.refund;
    
    if (!refund || !refund.refundSolution) {
      console.warn("⚠️ No refund solution found in XML");
      return null;
    }
    
    const refundSolution = refund.refundSolution;
    const pricing = refundSolution.pricing;
    
    // Extract refund amount and currency
    const refundPrice = refundSolution.$.refundPrice; // Format: "USD-513.48"
    const [currency, amountStr] = refundPrice.split('-');
    const refundAmount = parseFloat(amountStr);
    
    // Extract taxes
    const taxes = [];
    if (pricing.tax) {
      const taxArray = Array.isArray(pricing.tax) ? pricing.tax : [pricing.tax];
      taxArray.forEach(tax => {
        taxes.push({
          code: tax.$.code,
          amount: tax.displayPrice ? tax.displayPrice.$.amount : '0.00',
          currency: tax.displayPrice ? tax.displayPrice.$.currency : currency
        });
      });
    }
    
    // Extract conditions/notes
    const conditions = [];
    if (pricing.refundInfo && pricing.refundInfo.note) {
      conditions.push(pricing.refundInfo.note);
    }
    
    // Extract form of payment
    const formOfPayment = refundSolution.pricing.refundPrice.$.formOfPayment || 'UNKNOWN';
    
    // Determine eligibility
    const isEligible = refundAmount > 0;
    
    return {
      isEligible,
      refundAmount: Math.abs(refundAmount).toFixed(2),
      currency,
      message: isEligible 
        ? `✅ This booking is eligible for a refund of ${Math.abs(refundAmount).toFixed(2)} ${currency}.` 
        : "❌ This booking is not eligible for a refund.",
      policy: "Standard refund policy applies",
      fees: [],
      taxes,
      conditions,
      processingTime: "7-14 business days",
      formOfPayment
    };
  } catch (error) {
    console.error("❌ Error extracting refund info from XML:", error);
    return null;
  }
};

/**
 * Formats the refund eligibility response for display
 */
const formatEligibilityResponse = (data) => {
  try {
    if (!data) return "No eligibility data available";

    return {
      isEligible: data.isEligible,
      refundAmount: data.refundAmount,
      currency: data.currency,
      message: data.message || (data.isEligible 
        ? `✅ This booking is eligible for a refund of ${data.refundAmount} ${data.currency}.` 
        : "❌ This booking is not eligible for a refund."),
      policy: data.policy || "Standard refund policy applies",
      fees: data.fees || [],
      conditions: data.conditions || [],
      timeframe: data.processingTime || "7-14 business days",
      formOfPayment: data.formOfPayment || "Original payment method"
    };
  } catch (error) {
    console.error("Error formatting eligibility response:", error);
    return { message: "Error formatting eligibility details", error: error.message };
  }
};

/**
 * Transforms frontend data format to API request format
 */
const transformToApiRequest = (frontendData) => {
  console.log("🔄 Transforming frontend data:", JSON.stringify(frontendData, null, 2));
  
  // Handle both direct data object and {type, data} structure
  const data = frontendData.data || frontendData;
  
  if (!data) {
    console.warn("⚠️ No data found in input");
    return null;
  }

  // Just pass through the data as-is since the API expects this exact format
  return frontendData;
};

const checkRefundEligibility = new DynamicTool({
  name: "CheckRefundEligibility",
  description: `Use this tool to check if a booking is eligible for a refund.
  
The tool expects booking details in a structured format with flight details, passenger information, and payment details.`,

  func: async (input, config) => {
    console.group("🔍 [CheckRefundEligibility] Tool Invocation");
    console.log("📥 Raw input received:", typeof input, input);
    
    try {
      // Parse input with more detailed logging
      let parsedInput;
      if (typeof input === 'string') {
        console.log("📝 Input is a string, attempting to parse as JSON");
        try {
          parsedInput = JSON.parse(input);
          console.log("✅ Successfully parsed input:", parsedInput);
        } catch (e) {
          console.warn("⚠️ Failed to parse input as JSON:", e);
          return JSON.stringify({
            error: ERROR_MESSAGES.INVALID_INPUT
          });
        }
      } else {
        console.log("📝 Input is already an object:", input);
        parsedInput = input;
      }
      
      // Log the parsed input structure for debugging
      console.log("📊 Parsed input structure:", {
        hasData: !!parsedInput?.data,
        hasType: !!parsedInput?.type,
        dataType: typeof parsedInput?.data,
        keys: parsedInput ? Object.keys(parsedInput) : []
      });

      // More flexible validation with better debugging
      if (!parsedInput) {
        console.warn("⚠️ Input is null or undefined");
        return JSON.stringify({
          error: ERROR_MESSAGES.INVALID_INPUT,
          details: "Input is null or undefined"
        });
      }

      // Extract and validate PNR before proceeding
      const pnr = extractPnrFromInput(parsedInput);
      console.log("🔍 Extracted PNR:", pnr);

      if (pnr && !validatePnr(pnr)) {
        console.warn("⚠️ Invalid PNR format:", pnr);
        return JSON.stringify({
          error: ERROR_MESSAGES.INVALID_PNR,
          details: `PNR "${pnr}" is not valid. PNR should be exactly 6 characters long and contain only alphanumeric characters.`
        });
      }

      // Check if the input already has a data property or if it needs to be wrapped
      let apiRequest;
      if (parsedInput.data) {
        // Input already has a data property
        console.log("📝 Input already has a data property");
        apiRequest = parsedInput;
      } else if (parsedInput.flightDetails || parsedInput.passenger) {
        // Input is the data object itself, wrap it
        console.log("📝 Wrapping input in data property");
        apiRequest = { data: parsedInput };
      } else {
        console.warn("⚠️ Input doesn't have required properties");
        return JSON.stringify({
          error: ERROR_MESSAGES.INVALID_INPUT,
          details: "Expected input with flight details and passenger information"
        });
      }
      
      console.log("📤 Sending to refund eligibility API:", JSON.stringify(apiRequest, null, 2));

      // Call the API
      const response = await axios.post(
        `${API_CONFIG.baseURL}${API_CONFIG.endpoints.refundEligibility}`,
        apiRequest,
        { 
          timeout: API_CONFIG.timeout,
          headers: { 'Content-Type': 'application/json' }
        }
      );

      console.log(`✅ API responded with status: ${response.status}`);
      
      // Check if response is XML
      const isXmlResponse = typeof response.data === 'string' && 
                           (response.data.trim().startsWith('<?xml') || 
                            response.data.includes('<result'));
      
      console.log("📋 Response type:", typeof response.data);
      console.log("📋 Is XML response:", isXmlResponse);
      
      let data;
      
      if (isXmlResponse) {
        console.log("📋 Processing XML response");
        // Parse XML response
        data = await extractRefundInfoFromXML(response.data);
      } else {
        console.log("📋 Processing JSON response");
        // Use JSON response directly
        data = response.data;
      }
      
      console.log("📋 Eligibility response:", data);

      // Handle empty results
      if (!data) {
        console.warn("⚠️ No eligibility results found");
        return JSON.stringify({
          error: ERROR_MESSAGES.NO_RESULTS
        });
      }

      // Format and return results
      const formattedResponse = formatEligibilityResponse(data);
      
      return JSON.stringify({
        type: "refund_eligibility_result",
        data: data,
        formatted: formattedResponse
      });
    } catch (error) {
      console.error("❌ Error details:", {
        name: error.name,
        message: error.message,
        stack: error.stack,
        response: error.response?.data,
        request: error.request ? 'Request was made but no response received' : 'Request setup failed'
      });

      if (axios.isAxiosError(error)) {
        return JSON.stringify({
          error: ERROR_MESSAGES.API_ERROR,
          details: error.message
        });
      }

      return JSON.stringify({
        error: ERROR_MESSAGES.UNEXPECTED
      });
    } finally {
      console.groupEnd();
    }
  }
});

export default checkRefundEligibility;

// Add this at the end of the file for testing
// Uncomment and run this file directly to test
/*
const testInput = {
  "pnr": "ABC123", // Valid 6-character alphanumeric PNR
  "flightDetails": {
    "departureDate": "** July 16, 2025",
    "departureTime": "** 10:30",
    "arrivalTime": "** July 17, 2025, 00:05",
    "duration": "** 9 hours 5 minutes",
    "airline": "** Virgin Atlantic",
    "airlineCode": "VS",
    "flightNumber": "** VS 0302",
    "aircraft": "** Airbus A350-1000",
    "status": "** Confirmed"
  },
  "passenger": {
    "name": "** Beverly Cruz",
    "type": "** Adult (ADT)",
    "dateOfBirth": "** April 30, 1995",
    "gender": "** Male",
    "contact": "** +4498987898769",
    "ticketNumber": "** 9322328197721"
  }
};

// Test the tool directly
async function test() {
  const result = await checkRefundEligibility.func(testInput);
  console.log("Test result:", result);
}

// Test PNR validation
async function testPnrValidation() {
  console.log("Testing PNR validation...");

  // Test valid PNR
  const validPnrInput = { ...testInput, pnr: "ABC123" };
  console.log("Valid PNR test:", await checkRefundEligibility.func(validPnrInput));

  // Test invalid PNR - too short
  const shortPnrInput = { ...testInput, pnr: "ABC12" };
  console.log("Short PNR test:", await checkRefundEligibility.func(shortPnrInput));

  // Test invalid PNR - too long
  const longPnrInput = { ...testInput, pnr: "ABC1234" };
  console.log("Long PNR test:", await checkRefundEligibility.func(longPnrInput));

  // Test invalid PNR - contains special characters
  const specialCharPnrInput = { ...testInput, pnr: "ABC-12" };
  console.log("Special char PNR test:", await checkRefundEligibility.func(specialCharPnrInput));

  // Test invalid PNR - contains spaces
  const spacePnrInput = { ...testInput, pnr: "ABC 12" };
  console.log("Space PNR test:", await checkRefundEligibility.func(spacePnrInput));
}

// Uncomment to run tests
// test();
// testPnrValidation();
*/

