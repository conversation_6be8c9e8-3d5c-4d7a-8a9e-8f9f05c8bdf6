// Test script to verify PNR validation in checkRefundEligibility.js
import checkRefundEligibility from './orchestrator/agents/checkRefundEligibility.js';

const baseTestInput = {
  "flightDetails": {
    "departureDate": "July 16, 2025",
    "departureTime": "10:30",
    "arrivalTime": "July 17, 2025, 00:05",
    "duration": "9 hours 5 minutes",
    "airline": "Virgin Atlantic",
    "airlineCode": "VS",
    "flightNumber": "VS 0302",
    "aircraft": "Airbus A350-1000",
    "status": "Confirmed"
  },
  "passenger": {
    "name": "<PERSON> Cruz",
    "type": "Adult (ADT)",
    "dateOfBirth": "April 30, 1995",
    "gender": "Male",
    "contact": "+4498987898769",
    "ticketNumber": "9322328197721"
  }
};

async function testPnrValidation() {
  console.log("🧪 Testing PNR Validation in checkRefundEligibility\n");
  
  const testCases = [
    {
      name: "Valid PNR - 6 alphanumeric characters",
      pnr: "ABC123",
      shouldPass: true
    },
    {
      name: "Valid PNR - All letters",
      pnr: "ABCDEF",
      shouldPass: true
    },
    {
      name: "Valid PNR - All numbers",
      pnr: "123456",
      shouldPass: true
    },
    {
      name: "Invalid PNR - Too short (5 characters)",
      pnr: "ABC12",
      shouldPass: false
    },
    {
      name: "Invalid PNR - Too long (7 characters)",
      pnr: "ABC1234",
      shouldPass: false
    },
    {
      name: "Invalid PNR - Contains special characters",
      pnr: "ABC-12",
      shouldPass: false
    },
    {
      name: "Invalid PNR - Contains spaces",
      pnr: "ABC 12",
      shouldPass: false
    },
    {
      name: "Invalid PNR - Contains lowercase (mixed case)",
      pnr: "AbC123",
      shouldPass: true // This should actually pass as we accept both cases
    },
    {
      name: "No PNR provided",
      pnr: null,
      shouldPass: true // Should pass validation but may fail API call
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`\n📋 Testing: ${testCase.name}`);
    console.log(`   PNR: "${testCase.pnr}"`);
    console.log(`   Expected: ${testCase.shouldPass ? 'PASS' : 'FAIL'}`);
    
    try {
      const testInput = testCase.pnr ? { ...baseTestInput, pnr: testCase.pnr } : baseTestInput;
      const result = await checkRefundEligibility.func(testInput);
      const parsedResult = JSON.parse(result);
      
      const hasPnrError = parsedResult.error && parsedResult.error.includes('PNR is not correct');
      const actualResult = !hasPnrError;
      
      console.log(`   Actual: ${actualResult ? 'PASS' : 'FAIL'}`);
      console.log(`   Status: ${actualResult === testCase.shouldPass ? '✅ CORRECT' : '❌ INCORRECT'}`);
      
      if (hasPnrError) {
        console.log(`   Error: ${parsedResult.error}`);
      }
      
    } catch (error) {
      console.log(`   Status: ❌ ERROR - ${error.message}`);
    }
  }
  
  console.log("\n🏁 PNR Validation Testing Complete!");
}

// Run the test
testPnrValidation().catch(console.error);
