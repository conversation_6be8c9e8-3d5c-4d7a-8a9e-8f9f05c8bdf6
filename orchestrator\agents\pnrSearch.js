import { DynamicTool } from "langchain/tools";
import axios from "axios";

// Constants
const API_CONFIG = {
  baseURL: "http://localhost:5120", // Update this to match your API server
  endpoints: {
    pnrSearch: "/Refund/external-order-search"
  },
  timeout: 100000 // Increase timeout to match other services
};

const ERROR_MESSAGES = {
  INVALID_INPUT: "Invalid input. Please provide a valid PNR number.",
  INVALID_PNR: "❌ PNR is not correct. PNR should be 6 characters long and contain only alphanumeric characters.",
  API_ERROR: "❌ Failed to reach the PNR search service. Please try again later.",
  NO_RESULTS: "⚠️ No booking found for the given PNR.",
  UNEXPECTED: "❌ Something went wrong while searching for PNR details."
};

/**
 * Validates PNR format
 * @param {string} pnr - PNR to validate
 * @returns {boolean} Whether PNR is valid (6 characters, alphanumeric)
 */
const validatePnr = (pnr) => {
  if (!pnr || typeof pnr !== 'string') {
    return false;
  }

  const trimmedPnr = pnr.trim();

  // Check if PNR is exactly 6 characters long and contains only alphanumeric characters
  return /^[A-Za-z0-9]{6}$/.test(trimmedPnr);
};

/**
 * Validates that the total amount matches the sum of base fare, taxes, and fees
 * @param {Object} fareBreakdown - Fare breakdown object
 * @returns {Object} Validation result with isValid flag and details
 */
const validateFareAmountMatching = (fareBreakdown) => {
  try {
    if (!fareBreakdown || !fareBreakdown.baseFare || !fareBreakdown.total) {
      return {
        isValid: false,
        error: "Missing fare breakdown data for validation",
        details: "Cannot validate amounts without complete fare breakdown"
      };
    }

    const baseFareAmount = parseFloat(fareBreakdown.baseFare.amount || "0");
    const totalAmount = parseFloat(fareBreakdown.total.amount || "0");

    // Calculate sum of all taxes
    let taxesSum = 0;
    if (fareBreakdown.taxes && Array.isArray(fareBreakdown.taxes)) {
      taxesSum = fareBreakdown.taxes.reduce((sum, tax) => {
        return sum + parseFloat(tax.amount || "0");
      }, 0);
    }

    // Calculate sum of all fees
    let feesSum = 0;
    if (fareBreakdown.fees && Array.isArray(fareBreakdown.fees)) {
      feesSum = fareBreakdown.fees.reduce((sum, fee) => {
        return sum + parseFloat(fee.amount || "0");
      }, 0);
    }

    const calculatedTotal = baseFareAmount + taxesSum + feesSum;
    const tolerance = 0.01; // Allow for small rounding differences
    const difference = Math.abs(totalAmount - calculatedTotal);
    const isValid = difference <= tolerance;

    return {
      isValid,
      baseFare: baseFareAmount.toFixed(2),
      taxes: taxesSum.toFixed(2),
      fees: feesSum.toFixed(2),
      calculatedTotal: calculatedTotal.toFixed(2),
      apiTotal: totalAmount.toFixed(2),
      difference: difference.toFixed(2),
      currency: fareBreakdown.baseFare.currency || fareBreakdown.total.currency || "",
      message: isValid
        ? "✅ Fare amount validation passed - totals match"
        : `❌ Fare amount validation failed - difference of ${difference.toFixed(2)} ${fareBreakdown.baseFare.currency || ""}`
    };
  } catch (error) {
    return {
      isValid: false,
      error: "Error during fare validation",
      details: error.message
    };
  }
};

/**
 * Formats PNR search results for display
 * @param {Object} data - PNR search result data
 * @returns {Object} Formatted PNR details
 */
const formatPnrResults = (data) => {
  if (!data) return null;
  
  try {
    // Log the raw segment details from the API response
    console.log("🔍 Raw segment details from API:", JSON.stringify(data.segmentDetails || [], null, 2));
    console.log("🔍 Segment details array length:", data.segmentDetails?.length || 0);
    
    // Extract fare breakdown for taxes
    const fareBreakdown = extractFareBreakdown(data);
    
    // Ensure segment details are properly formatted
    const segmentDetails = data.segmentDetails?.map(segment => {
      console.log("🔍 Processing segment:", segment.marketingFlightNumber || "unknown");
      return {
        airCraft: segment.airCraft || "",
        marketingCarrier: segment.marketingCarrier || "",
        marketingFlightNumber: segment.marketingFlightNumber || "",
        originDestinationRefId: segment.originDestinationRefId || "",
        originAirportCode: segment.originAirportCode || "",
        originAirportName: segment.originAirportName || "",
        originCityCode: segment.originCityCode || "",
        originCountryCode: segment.originCountryCode || "",
        originCountryName: segment.originCountryName || "",
        destinationAirportCode: segment.destinationAirportCode || "",
        destinationAirportName: segment.destinationAirportName || "",
        destinationCityCode: segment.destinationCityCode || "",
        destinationCountryCode: segment.destinationCountryCode || "",
        destinationCountryName: segment.destinationCountryName || "",
        classOfService: segment.classOfService || "",
        scheduledDepartureTime: segment.scheduledDepartureTime || {},
        scheduledArrivalTime: segment.scheduledArrivalTime || {},
        flightDuration: segment.flightDuration || "",
        cabinType: segment.cabinType || {},
        status: segment.status || {}
      };
    }) || [];
    
    console.log("📋 Formatted segment details count:", segmentDetails.length);
    console.log("📋 First segment (if exists):", segmentDetails.length > 0 ? JSON.stringify(segmentDetails[0], null, 2) : "No segments");
    
    // Format the response in the exact structure needed for frontend display
    const formattedResponse = {
      message: `🛫 **Flight Details:**
- **Origin:** ${data.segmentDetails?.[0]?.originAirportName || ""} (${data.segmentDetails?.[0]?.originAirportCode || ""}), ${data.segmentDetails?.[0]?.originCityCode || ""}, ${data.segmentDetails?.[0]?.originCountryName || ""}
- **Destination:** ${data.segmentDetails?.[0]?.destinationAirportName || ""} (${data.segmentDetails?.[0]?.destinationAirportCode || ""}), ${data.segmentDetails?.[0]?.destinationCityCode || ""}, ${data.segmentDetails?.[0]?.destinationCountryName || ""}
- **Airline:** ${data.segmentDetails?.[0]?.marketingCarrier || ""}
- **Flight Number:** ${data.segmentDetails?.[0]?.marketingFlightNumber || ""}
- **Departure Date:** ${formatDate(data.originDestinationAssociations?.[0]?.originDestination?.departureDate)}
- **Departure Time:** ${formatTime(data.segmentDetails?.[0]?.scheduledDepartureTime?.localDateTime)}
- **Arrival Time:** ${formatTime(data.segmentDetails?.[0]?.scheduledArrivalTime?.localDateTime)}
- **Duration:** ${data.segmentDetails?.[0]?.flightDuration || ""}
- **Aircraft:** ${data.segmentDetails?.[0]?.airCraft || ""}
- **Cabin:** ${data.segmentDetails?.[0]?.cabinType?.cabinName || ""}
- **Status:** ${data.segmentDetails?.[0]?.status?.description || ""}

👥 **Passengers:**
${formatPassengers(data.customerDetails, data.orderItems)}

✈️ **Segment Details:**
${formatSegmentDetails(data.segmentDetails)}

💳 **Payment:**
- **Total Amount:** ${data.totalPrice?.monetaryAmount || ""} ${data.totalPrice?.baseCurrency || ""}
- **Method:** ${data.paymentDetails?.[0]?.formOfPayment || ""}
- **Base Fare:** ${fareBreakdown?.baseFare?.amount || ""} ${fareBreakdown?.baseFare?.currency || ""}
- **Taxes:**
${formatTaxes(fareBreakdown?.taxes)}

🔍 **Fare Validation:**
${formatFareValidation(validateFareAmountMatching(fareBreakdown))}

🛄 **Baggage Allowance:**
${formatBaggage(data.productComponents)}

🍽️ **Services:**
${formatServices(data.productComponents)}

📝 **Note:** The booking includes various services and amenities for a comfortable journey.`,
      
      // Include the structured data for programmatic use
      data: {
        flight: {
          origin: {
            code: data.segmentDetails?.[0]?.originAirportCode || "",
            name: data.segmentDetails?.[0]?.originAirportName || "",
            city: data.segmentDetails?.[0]?.originCityCode || "",
            country: data.segmentDetails?.[0]?.originCountryName || ""
          },
          destination: {
            code: data.segmentDetails?.[0]?.destinationAirportCode || "",
            name: data.segmentDetails?.[0]?.destinationAirportName || "",
            city: data.segmentDetails?.[0]?.destinationCityCode || "",
            country: data.segmentDetails?.[0]?.destinationCountryName || ""
          },
          airline: data.segmentDetails?.[0]?.marketingCarrier || "",
          flightNumber: data.segmentDetails?.[0]?.marketingFlightNumber || "",
          departureDate: data.originDestinationAssociations?.[0]?.originDestination?.departureDate || "",
          departureTime: data.segmentDetails?.[0]?.scheduledDepartureTime?.localDateTime || "",
          arrivalTime: data.segmentDetails?.[0]?.scheduledArrivalTime?.localDateTime || "",
          duration: data.segmentDetails?.[0]?.flightDuration || "",
          aircraft: data.segmentDetails?.[0]?.airCraft || "",
          cabin: data.segmentDetails?.[0]?.cabinType?.cabinName || "",
          status: data.segmentDetails?.[0]?.status?.description || ""
        },
        passengers: data.customerDetails?.map(passenger => ({
          id: passenger.custId || "",
          firstName: passenger.firstName || "",
          lastName: passenger.lastName || "",
          type: passenger.ptc || "",
          dateOfBirth: passenger.dateOfBirth || "",
          gender: passenger.gender || "",
          contact: passenger.contacts?.phone || "",
          seat: findSeatAssignment(data.orderItems, passenger.custId),
          ticket: findTicketNumber(data.orderItems, passenger.custId)
        })) || [],
        payment: {
          totalAmount: data.totalPrice?.monetaryAmount || "",
          currency: data.totalPrice?.baseCurrency || "",
          method: data.paymentDetails?.[0]?.formOfPayment || "",
          baseFare: fareBreakdown?.baseFare?.amount || "",
          taxes: fareBreakdown?.taxes || [],
          fees: fareBreakdown?.fees || []
        },
        // Add fare amount validation
        fareValidation: validateFareAmountMatching(fareBreakdown),
        services: extractServices(data.productComponents),
        // Explicitly include the segment details array
        segmentDetails: segmentDetails
      }
    };
    
    return formattedResponse;
  } catch (error) {
    console.error("Error formatting PNR data:", error);
    return { message: "Error formatting booking details", error: error.message };
  }
};

/**
 * Find seat assignment for a passenger
 * @param {Array} orderItems - Order items from PNR data
 * @param {string} customerId - Customer ID to find seat for
 * @returns {string} Seat assignment or empty string
 */
const findSeatAssignment = (orderItems, customerId) => {
  if (!orderItems || !Array.isArray(orderItems)) return "";
  
  for (const item of orderItems) {
    if (item.services && Array.isArray(item.services)) {
      for (const service of item.services) {
        if (
          service.productOrder?.productType === "Seat" && 
          service.serviceBeneficiaries?.includes(customerId) &&
          service.currentSeatAssignment
        ) {
          return service.currentSeatAssignment;
        }
      }
    }
  }
  
  return "";
};

/**
 * Find ticket number for a passenger
 * @param {Array} orderItems - Order items from PNR data
 * @param {string} customerId - Customer ID to find ticket for
 * @returns {string} Ticket number or empty string
 */
const findTicketNumber = (orderItems, customerId) => {
  if (!orderItems || !Array.isArray(orderItems)) return "";
  
  for (const item of orderItems) {
    if (item.accountableDocumentRefs && Array.isArray(item.accountableDocumentRefs)) {
      for (const doc of item.accountableDocumentRefs) {
        if (
          doc.documentType === "Ticket" && 
          doc.customerRefId === customerId &&
          doc.documentNumber
        ) {
          return doc.documentNumber;
        }
      }
    }
  }
  
  return "";
};

/**
 * Extract key services and amenities
 * @param {Array} components - Product components from PNR data
 * @returns {Object} Categorized services
 */
const extractServices = (components) => {
  if (!components || !Array.isArray(components)) return {};
  
  const services = {
    baggage: [],
    meals: [],
    entertainment: [],
    seating: [],
    other: []
  };
  
  for (const component of components) {
    const desc = component.description || "";
    
    if (desc.includes("Baggage")) {
      services.baggage.push(desc);
    } else if (desc.includes("Meal") || desc.includes("Drinks") || desc.includes("Food")) {
      services.meals.push(desc);
    } else if (desc.includes("Entertainment") || desc.includes("Screen") || desc.includes("Wi-Fi")) {
      services.entertainment.push(desc);
    } else if (desc.includes("Seat") || desc.includes("Legroom")) {
      services.seating.push(desc);
    } else {
      services.other.push(desc);
    }
  }
  
  return services;
};

/**
 * Extract complete fare breakdown including taxes
 * @param {Object} data - PNR data
 * @returns {Object} Complete fare breakdown
 */
const extractFareBreakdown = (data) => {
  if (!data || !Array.isArray(data.orderItems)) return {};

  try {
    // Aggregate all priceDetails from all orderItems
    const allPriceDetails = data.orderItems
      .flatMap(item => item.priceDetails || []);

    // Group and sum by priceComponentType and priceComponentName
    const taxes = [];
    const fees = [];
    let baseFareAmount = 0;
    let baseCurrency = data.totalPrice?.baseCurrency || "";

    allPriceDetails.forEach(detail => {
      const type = detail.priceComponentType;
      const name = detail.priceComponentName || "";
      const amount = parseFloat(detail.amount?.monetaryAmount || "0");
      const currency = detail.amount?.baseCurrency || baseCurrency;

      if (type === "BaseFare") {
        baseFareAmount += amount;
      } else if (type === "TotalTax") {
        taxes.push({
          code: name,
          description: name,
          amount: detail.amount?.monetaryAmount || "",
          currency: currency
        });
      } else if (type === "Fee") {
        fees.push({
          code: name,
          description: name,
          amount: detail.amount?.monetaryAmount || "",
          currency: currency
        });
      }
    });

    return {
      baseFare: {
        amount: baseFareAmount.toFixed(2),
        currency: baseCurrency
      },
      taxes,
      fees,
      total: {
        amount: data.totalPrice?.monetaryAmount || "",
        currency: baseCurrency
      }
    };
  } catch (error) {
    console.error("Error extracting fare breakdown:", error);
    return {};
  }
};

const extractPriceDetails = (data) => {
  if (!data || !Array.isArray(data.orderItems)) return [];

  try {
    return data.orderItems.flatMap(item =>
      (item.priceDetails || []).map(detail => ({
        type: detail.priceComponentType === "TotalTax" ? "Tax"
             : detail.priceComponentType === "Fee" ? "Fee"
             : detail.priceComponentType || "Other",
        code: detail.priceComponentName || "",
        description: detail.priceComponentName || "",
        amount: detail.amount?.monetaryAmount || "",
        currency: detail.amount?.baseCurrency || data.totalPrice?.baseCurrency || ""
      }))
    );
  } catch (err) {
    console.error("❌ Error extracting price details:", err);
    return [];
  }
};

// Helper functions for formatting
const formatDate = (dateString) => {
  if (!dateString) return "";
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  } catch (e) {
    return dateString;
  }
};

const formatTime = (timeString) => {
  if (!timeString) return "";
  try {
    const date = new Date(timeString);
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false
    });
  } catch (e) {
    return timeString;
  }
};

const formatPassengers = (passengers, orderItems) => {
  if (!passengers || !Array.isArray(passengers)) return "";
  
  return passengers.map((passenger, index) => {
    const seat = findSeatAssignment(orderItems, passenger.custId);
    const ticket = findTicketNumber(orderItems, passenger.custId);
    
    return `${index + 1}. **${passenger.firstName || ""} ${passenger.lastName || ""}**
   - **Type:** ${formatPassengerType(passenger.ptc) || ""}
   - **DOB:** ${formatDate(passenger.dateOfBirth) || ""}
   - **Gender:** ${formatGender(passenger.gender) || ""}
   ${passenger.contacts?.phone ? `- **Contact:** ${passenger.contacts.phone}\n   ` : ""}
   ${seat ? `- **Seat:** ${seat}\n   ` : ""}
   ${ticket ? `- **Ticket:** ${ticket}` : ""}`;
  }).join("\n\n");
};

const formatPassengerType = (type) => {
  const types = {
    "ADT": "Adult (ADT)",
    "CHD": "Child (CHD)",
    "INF": "Infant (INF)",
    "GBE": "Group Booking (GBE)"
  };
  return types[type] || type;
};

const formatGender = (gender) => {
  if (gender === "M") return "Male";
  if (gender === "F") return "Female";
  return gender;
};

const formatTaxes = (taxes) => {
  if (!taxes || !Array.isArray(taxes) || taxes.length === 0) return "  - No tax details available";

  return taxes.map(tax => `  - ${tax.code}: ${tax.amount} ${tax.currency}`).join("\n");
};

const formatFareValidation = (validation) => {
  if (!validation) return "- Validation data not available";

  if (validation.error) {
    return `- ❌ ${validation.error}: ${validation.details || ""}`;
  }

  const result = `- ${validation.message}
- **Breakdown:** Base Fare: ${validation.baseFare} + Taxes: ${validation.taxes} + Fees: ${validation.fees} = ${validation.calculatedTotal} ${validation.currency}
- **API Total:** ${validation.apiTotal} ${validation.currency}`;

  if (!validation.isValid) {
    return result + `\n- **⚠️ Difference:** ${validation.difference} ${validation.currency}`;
  }

  return result;
};

const formatBaggage = (components) => {
  if (!components || !Array.isArray(components)) return "- No baggage information available";
  
  const baggage = components
    .filter(comp => comp.description && comp.description.includes("Baggage"))
    .map(comp => `- ${comp.description}`);
  
  return baggage.length > 0 ? baggage.join("\n") : "- Standard baggage allowance";
};

const formatServices = (components) => {
  if (!components || !Array.isArray(components)) return "- No service information available";
  
  const services = extractServices(components);
  
  let result = "";
  
  if (services.meals.length > 0) {
    result += `- **Meals:** ${services.meals.join(", ")}\n`;
  }
  
  if (services.entertainment.length > 0) {
    result += `- **Entertainment:** ${services.entertainment.join(", ")}\n`;
  }
  
  if (services.seating.length > 0) {
    result += `- **Seating:** ${services.seating.join(", ")}\n`;
  }
  
  if (services.other.length > 0) {
    result += `- **Others:** ${services.other.join(", ")}`;
  }
  
  return result || "- No additional services";
};

/**
 * Formats segment details for display
 * @param {Array} segments - Segment details from PNR data
 * @returns {string} Formatted segment details
 */
const formatSegmentDetails = (segments) => {
  if (!segments || !Array.isArray(segments) || segments.length === 0) {
    return "- No segment details available";
  }
  
  return segments.map((segment, index) => {
    return `${index + 1}. **${segment.marketingCarrier || ""} ${segment.marketingFlightNumber || ""}**
   - **Origin:** ${segment.originAirportName || ""} (${segment.originAirportCode || ""})
   - **Destination:** ${segment.destinationAirportName || ""} (${segment.destinationAirportCode || ""})
   - **Aircraft:** ${segment.airCraft || ""}
   - **Class:** ${segment.classOfService || ""}
   - **Departure:** ${formatTime(segment.scheduledDepartureTime?.localDateTime)}
   - **Arrival:** ${formatTime(segment.scheduledArrivalTime?.localDateTime)}
   - **Duration:** ${segment.flightDuration || ""}
   - **Status:** ${segment.status?.description || ""}`;
  }).join("\n\n");
};

// Main Tool Definition
const pnrSearch = new DynamicTool({
  name: "PNRSearch",
  description: `Use this to search for a booking using a PNR (Passenger Name Record) number.

Required:
- pnr (string): The booking reference number (must be exactly 6 characters long and contain only alphanumeric characters)
`,

  func: async (input) => {
    console.group("🔍 [PNRSearch] Tool Invocation");
    console.log("📥 Raw input received:", input);

    try {
      // Extract PNR from input
      let pnr = "";
      
      if (typeof input === 'string') {
        // Try to extract PNR from string - use broader pattern for extraction, validation will check exact format
        const match = input.match(/\b([A-Z0-9]{4,8})\b/i);
        if (match) {
          pnr = match[1];
        } else {
          // If no match, try to parse as JSON
          try {
            const parsed = JSON.parse(input);
            pnr = parsed.pnr || "";
          } catch (e) {
            // If not valid JSON, use the whole string
            pnr = input.trim();
          }
        }
      } else if (typeof input === 'object' && input !== null) {
        pnr = input.pnr;
      }

      console.log("🔍 Extracted PNR:", pnr);

      // Validate PNR
      if (!validatePnr(pnr)) {
        console.warn("⚠️ Invalid PNR format:", pnr);
        return JSON.stringify({
          error: ERROR_MESSAGES.INVALID_PNR
        });
      }

      console.log("🔍 Searching for PNR:", pnr);

      // Call API with more detailed logging
      console.log(`🔍 Calling API: ${API_CONFIG.baseURL}${API_CONFIG.endpoints.pnrSearch}`);
      console.log(`🔍 Request payload:`, { Rloc: pnr });
      
      const response = await axios.post(
        `${API_CONFIG.baseURL}${API_CONFIG.endpoints.pnrSearch}`,
        { Rloc: pnr },
        { timeout: API_CONFIG.timeout }
      );

      console.log(`✅ API responded with status: ${response.status}`);
      
      const data = response.data;

      // Check if segmentDetails exists in the API response
      console.log("🔍 API response has segmentDetails:", !!data.segmentDetails);
      console.log("🔍 segmentDetails type:", data.segmentDetails ? typeof data.segmentDetails : "N/A");
      console.log("🔍 segmentDetails is array:", data.segmentDetails ? Array.isArray(data.segmentDetails) : "N/A");
      console.log("🔍 segmentDetails length:", data.segmentDetails ? data.segmentDetails.length : 0);
      
      if (data.segmentDetails && data.segmentDetails.length > 0) {
        console.log("🔍 First segment sample:", JSON.stringify(data.segmentDetails[0], null, 2));
      }
      
      // Handle empty results
      if (!data) {
        console.warn("⚠️ No PNR results found");
        return JSON.stringify({
          error: ERROR_MESSAGES.NO_RESULTS
        });
      }

      // Format the results
      const formattedResults = formatPnrResults(data);
      
      // Ensure segment details are included in the response
      if (!formattedResults.data.segmentDetails && data.segmentDetails) {
        console.log("📋 Adding segment details to response");
        formattedResults.data.segmentDetails = data.segmentDetails.map(segment => ({
          airCraft: segment.airCraft || "",
          marketingCarrier: segment.marketingCarrier || "",
          marketingFlightNumber: segment.marketingFlightNumber || "",
          originDestinationRefId: segment.originDestinationRefId || "",
          originAirportCode: segment.originAirportCode || "",
          originAirportName: segment.originAirportName || "",
          originCityCode: segment.originCityCode || "",
          originCountryCode: segment.originCountryCode || "",
          originCountryName: segment.originCountryName || "",
          destinationAirportCode: segment.destinationAirportCode || "",
          destinationAirportName: segment.destinationAirportName || "",
          destinationCityCode: segment.destinationCityCode || "",
          destinationCountryCode: segment.destinationCountryCode || "",
          destinationCountryName: segment.destinationCountryName || "",
          classOfService: segment.classOfService || "",
          scheduledDepartureTime: segment.scheduledDepartureTime || {},
          scheduledArrivalTime: segment.scheduledArrivalTime || {},
          flightDuration: segment.flightDuration || "",
          cabinType: segment.cabinType || {},
          status: segment.status || {}
        }));
      }
      
      // Log the final response structure
      console.log("📋 Final response structure:", {
        hasSegmentDetails: !!formattedResults.data.segmentDetails,
        segmentDetailsCount: formattedResults.data.segmentDetails?.length || 0,
        dataKeys: Object.keys(formattedResults.data)
      });

      // Return the complete data structure
      return JSON.stringify({
        type: "pnr_details",
        ...formattedResults  // Include all formatted data
      });
    } catch (error) {
      console.error("❌ Error details:", {
        name: error.name,
        message: error.message,
        stack: error.stack,
        response: error.response?.data
      });

      if (axios.isAxiosError(error)) {
        if (error.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          return JSON.stringify({
            error: `❌ API Error (${error.response.status}): ${error.response.data?.message || 'Unknown error'}`
          });
        } else if (error.request) {
          // The request was made but no response was received
          return JSON.stringify({
            error: "❌ No response from PNR search service. Please check if the service is running."
          });
        } else {
          // Something happened in setting up the request that triggered an Error
          return JSON.stringify({
            error: ERROR_MESSAGES.API_ERROR
          });
        }
      }

      return JSON.stringify({
        error: ERROR_MESSAGES.UNEXPECTED
      });
    } finally {
      console.groupEnd();
    }
  }
});

export default pnrSearch;






















